# 钉钉机器人服务

这是一个基于 FastAPI 的钉钉机器人消息接收与处理服务，旨在为钉钉机器人提供标准化的消息接收、解析和处理能力。

## 功能特性

- 接收钉钉机器人 Webhook 消息
- 解析钉钉消息结构（文本消息、@用户信息等）
- 请求头验证（timestamp、sign）
- 日志记录
- 异常处理
- 提供健康检查接口
- 使用 SQLite 数据库记录消息
- 向指定钉钉群推送消息

## 目录结构

```
backend/
├── main.py           # FastAPI主应用文件
├── models.py         # Pydantic数据模型定义
├── requirements.txt  # Python依赖包
└── README.md        # 项目说明文档
```

## 快速开始

### 环境要求

- Python 3.9+
- pip

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置

1. 修改 [config.py](file:///Users/<USER>/workspace/moyy-dingding-agent/config.py) 中的配置或设置环境变量：
   - `DINGTALK_WEBHOOK_URL`: 目标钉钉群的 webhook 地址
   - `DINGTALK_WEBHOOK_SECRET`: 目标钉钉群的 webhook 密钥

### 启动服务

开发模式启动：

```bash
python main.py
```

或者使用 uvicorn 启动：

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

生产环境启动：

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

使用脚本启动：

```bash
chmod +x start.sh
./start.sh
```

### 3. 访问接口文档

启动后访问：http://localhost:8000/docs 查看API文档

## API接口

### 健康检查

- **URL**: `GET /`
- **描述**: 检查服务运行状态
- **响应**: 
  ```json
  {
    "message": "钉钉机器人服务运行正常",
    "status": "healthy"
  }
  ```

### 钉钉消息接收

- **URL**: `POST /dingtalk/webhook`
- **描述**: 接收钉钉机器人消息的Webhook接口
- **请求头**:
  ```json
  {
    "Content-Type": "application/json; charset=utf-8",
    "timestamp": "1577262236757",
    "sign": "xxxxxxxxxx"
  }
  ```
- **请求体**: 钉钉标准消息格式（详见models.py）
- **响应**:
  ```json
  {
    "success": true,
    "message": "消息处理成功",
    "data": {
      "received_message": {...},
      "reply": "回复内容"
    }
  }
  ```

## 数据库

使用 SQLite 作为存储后端，数据库文件为 `dingtalk_messages.db`，包含以下字段：

- id: 记录ID
- msg_id: 消息ID
- sender_id: 发送者ID
- sender_nick: 发送者昵称
- conversation_id: 会话ID
- conversation_type: 会话类型
- conversation_title: 会话标题
- content: 消息内容
- msg_type: 消息类型
- create_at: 消息创建时间
- timestamp: 时间戳
- is_admin: 是否为管理员
- is_reply: 是否为回复消息
- replied_content: 被回复消息内容
- original_msg_id: 原始消息ID
- at_users: 被@的用户信息
- created_at: 记录创建时间

- `msgtype`: 消息类型（目前只支持text）
- `text.content`: 消息文本内容
- `msgId`: 加密的消息ID
- `senderId`: 加密的发送者ID
- `senderNick`: 发送者昵称
- `conversationId`: 加密的会话ID
- `conversationType`: 会话类型（1：单聊，2：群聊）
- `sessionWebhook`: 当前会话的Webhook地址
- `atUsers`: 被@的用户信息列表

### DingTalkHeaders

请求头信息：

- `timestamp`: 消息发送的时间戳（毫秒）
- `sign`: 签名值

## 消息推送

当收到新消息时，服务会自动向配置的钉钉群推送通知消息。

```python
async def process_message(message: DingTalkMessage, headers: DingTalkHeaders):
    # 在这里添加您的业务逻辑
    # 例如：
    # - 调用AI服务生成回复
    # - 查询数据库获取信息
    # - 调用外部API
    # - 发送消息到其他系统
    pass
```

## 日志

服务会自动记录以下信息：
- 接收到的请求头
- 接收到的消息体
- 消息处理详情
- 错误信息

## 错误处理

服务提供完整的错误处理机制：
- 请求头验证
- 消息体格式验证
- 业务逻辑异常处理
- 统一的错误响应格式

## 部署

### Docker 部署

```dockerfile
FROM python:3.9
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Nginx 反向代理配置

```nginx
location /dingtalk {
    proxy_pass http://localhost:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

## 安全说明

- 建议在生产环境添加签名验证逻辑
- 所有请求需通过钉钉 Webhook 发起