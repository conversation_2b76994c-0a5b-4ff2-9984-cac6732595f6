from __future__ import annotations
from pydantic import BaseModel
from typing import List, Optional, Union, Dict, Any


class AtUser(BaseModel):
    """被@的用户信息"""
    dingtalkId: str  # 加密的发送者ID
    staffId: Optional[str] = None     # 当前企业内部群中的员工userid (可选，不是所有情况都有)


class TextContent(BaseModel):
    """消息文本内容"""
    content: str  # 消息文本
    isReplyMsg: Optional[bool] = None               # 是否为回复消息
    repliedMsg: Optional[RepliedMsg] = None         # 被回复的消息信息


class RichTextItem(BaseModel):
    """富文本项目"""
    content: Optional[str] = None                 # 文本内容
    msgType: Optional[str] = None                 # 消息类型：text, picture等
    text: Optional[str] = None                    # 文本内容（兼容旧格式）
    type: Optional[str] = None                    # 类型：picture等（兼容旧格式）
    pictureDownloadCode: Optional[str] = None     # 图片下载码
    downloadCode: Optional[str] = None            # 下载码


class RichTextContent(BaseModel):
    """富文本内容"""
    richText: List[RichTextItem]  # 富文本项目列表


class RepliedMsg(BaseModel):
    """被回复的消息"""
    createdAt: int                                  # 原消息创建时间戳
    senderId: str                                   # 原消息发送者ID
    msgType: str                                    # 原消息类型
    msgId: str                                      # 原消息ID
    content: Optional[Union[str, RichTextContent, Dict[str, Any]]] = None  # 原消息内容


class DingTalkMessage(BaseModel):
    """钉钉消息结构"""
    msgtype: str                           # text, richText等
    content: Optional[Union[str, RichTextContent, Dict[str, Any]]] = None  # 消息内容，可以是字符串或富文本对象
    text: Optional[TextContent] = None     # 消息文本对象
    msgId: str                            # 加密的消息ID
    createAt: Union[str, int]             # 消息的时间戳，单位ms (可以是字符串或整数)
    conversationType: str                 # 1：单聊 2：群聊
    conversationId: str                   # 加密的会话ID
    conversationTitle: Optional[str] = None  # 群聊时才有的会话标题
    senderId: str                         # 加密的发送者ID
    senderNick: str                       # 发送者昵称
    senderCorpId: Optional[str] = None    # 企业内部群有的发送者当前群的企业corpId
    sessionWebhook: str                   # 当前会话的Webhook地址
    sessionWebhookExpiredTime: int        # 当前会话的Webhook地址过期时间
    isAdmin: Optional[bool] = None        # 是否为管理员
    chatbotCorpId: Optional[str] = None   # 加密的机器人所在的企业corpId
    isInAtList: Optional[bool] = None     # 是否在@列表中
    senderStaffId: Optional[str] = None   # 该字段在机器人发布线上版本时，才会返回
    chatbotUserId: str                    # 加密的机器人ID
    atUsers: Optional[List[AtUser]] = []  # 被@的人的信息
    # 添加实际webhook中可能出现的其他字段
    senderPlatform: Optional[str] = None  # 发送者平台信息
    robotCode: Optional[str] = None       # 机器人代码
    originalMsgId: Optional[str] = None   # 当消息为回复消息时，原消息的ID


class DingTalkHeaders(BaseModel):
    """钉钉请求头信息"""
    timestamp: str  # 消息发送的时间戳，单位是毫秒
    sign: str       # 签名值 