sh: warning: setlocale: LC_ALL: cannot change locale (zh_CN.utf8)
sh: warning: setlocale: LC_ALL: cannot change locale (zh_CN.utf8)
  File "main.py", line 9
SyntaxError: Non-ASCII character '\xe9' in file main.py on line 9, but no encoding declared; see http://www.python.org/peps/pep-0263.html for details
INFO:     Started server process [404]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8900 (Press CTRL+C to quit)
INFO:     **************:32005 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:32005 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:47031 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:9982 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:54857 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:23164 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:22139 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:22139 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:51100 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:17650 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:38180 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:34788 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:     **************:39741 - "GET /moye/dingtalk/webhook HTTP/1.1" 405 Method Not Allowed
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '662', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 'pJecgzEW6fRM7ih+w9lFmYxAvcUIWakD9+V7VrZlNX8=', 'timestamp': '1749202456942', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-rmjs6', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Android",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msgYMkPahWCsiS+vB1YnfTEVA==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749207856927,
  "createAt": 1749202456739,
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=e20c78035e4ae763ed3567bb3c5020c7",
  "text": {
    "content": " 你好"
  },
  "robotCode": "normal",
  "msgtype": "text"
}
ERROR:__main__:解析消息体失败: 2 validation errors for DingTalkMessage
createAt
  Input should be a valid string [type=string_type, input_value=1749202456739, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
atUsers.0.staffId
  Field required [type=missing, input_value={'dingtalkId': '$:LWCP_v1...b3K81Dh+vx2pnnMSIAgG7b'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
INFO:     **************:40851 - "POST /moye/dingtalk/webhook HTTP/1.1" 400 Bad Request
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '662', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 'Rct1XU8Kwct7sLIFga+ym7NgIvh2cJk9aLLwxMjU0nA=', 'timestamp': '1749202680983', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-rmjs6', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Android",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msg0viTgfX7vyr0npPmDHjW/Q==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749208080968,
  "createAt": 1749202680757,
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=e20c78035e4ae763ed3567bb3c5020c7",
  "text": {
    "content": " 你好"
  },
  "robotCode": "normal",
  "msgtype": "text"
}
ERROR:__main__:解析消息体失败: 2 validation errors for DingTalkMessage
createAt
  Input should be a valid string [type=string_type, input_value=1749202680757, input_type=int]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
atUsers.0.staffId
  Field required [type=missing, input_value={'dingtalkId': '$:LWCP_v1...b3K81Dh+vx2pnnMSIAgG7b'}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/missing
INFO:     **************:21469 - "POST /moye/dingtalk/webhook HTTP/1.1" 400 Bad Request
INFO:     Started server process [48233]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started server process [48705]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8900 (Press CTRL+C to quit)
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '662', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 'fPuCZQxA+V346SYraZlENS3XYNGqytrp+xFrMGnI+u0=', 'timestamp': '1749202851751', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-4f9kr', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Android",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msghDFhxNj5f2a9r89FvQ4Ksw==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749208251738,
  "createAt": 1749202851515,
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=e20c78035e4ae763ed3567bb3c5020c7",
  "text": {
    "content": " 你好"
  },
  "robotCode": "normal",
  "msgtype": "text"
}
INFO:__main__:处理消息 - 发送者: 林伟, 内容: 你好
/TRS/moye-dingding-agent/main.py:103: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
  "atUsers": [user.dict() for user in message.atUsers] if message.atUsers else []
INFO:__main__:消息详情: {
  "msgId": "msghDFhxNj5f2a9r89FvQ4Ksw==",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "senderNick": "林伟",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "conversationType": "群聊",
  "content": "你好",
  "timestamp": "1749202851751",
  "isAdmin": false,
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
      "staffId": null
    }
  ]
}
INFO:     **************:57800 - "POST /moye/dingtalk/webhook HTTP/1.1" 200 OK
INFO:     Started server process [49866]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8900 (Press CTRL+C to quit)
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '662', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 'ViY8iHRhwcZnqOp7HIm4EB2hXCkbP2XVI4zx7IU9hts=', 'timestamp': '1749203200727', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-5jjwt', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Android",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msgKV8IK0tFYl740P6R1fE8lg==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749208600712,
  "createAt": 1749203200442,
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=e20c78035e4ae763ed3567bb3c5020c7",
  "text": {
    "content": " 你好"
  },
  "robotCode": "normal",
  "msgtype": "text"
}
INFO:__main__:处理消息 - 发送者: 林伟, 内容: 你好
/TRS/moye-dingding-agent/main.py:103: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
  "atUsers": [user.dict() for user in message.atUsers] if message.atUsers else []
INFO:__main__:消息详情: {
  "msgId": "msgKV8IK0tFYl740P6R1fE8lg==",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "senderNick": "林伟",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "conversationType": "群聊",
  "content": "你好",
  "timestamp": "1749203200727",
  "isAdmin": false,
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
      "staffId": null
    }
  ]
}
INFO:     **************:55529 - "POST /moye/dingtalk/webhook HTTP/1.1" 200 OK
INFO:     Started server process [51116]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8900 (Press CTRL+C to quit)
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '662', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 'VHK9+5Bg7NyFk8PVSbHE92Ie04e45X9hgtiMEWYixqo=', 'timestamp': '1749203563618', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-62ckv', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Android",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msgNh9mA1nbdew3w+dxuWFvYQ==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749208963604,
  "createAt": 1749203563330,
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=e20c78035e4ae763ed3567bb3c5020c7",
  "text": {
    "content": " 你好"
  },
  "robotCode": "normal",
  "msgtype": "text"
}
INFO:__main__:处理消息 - 发送者: 林伟, 内容: 你好
/TRS/moye-dingding-agent/main.py:122: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
  "atUsers": [user.dict() for user in message.atUsers] if message.atUsers else []
INFO:__main__:消息详情: {
  "msgId": "msgNh9mA1nbdew3w+dxuWFvYQ==",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "senderNick": "林伟",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "conversationType": "群聊",
  "content": "你好",
  "timestamp": "1749203563618",
  "isAdmin": false,
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
      "staffId": null
    }
  ]
}
INFO:     **************:1346 - "POST /moye/dingtalk/webhook HTTP/1.1" 200 OK
INFO:     **************:55678 - "GET /sitemap.xml HTTP/1.1" 404 Not Found
INFO:     **************:55678 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:55678 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     **************:44215 - "GET / HTTP/1.1" 200 OK
INFO:     **************:39896 - "GET / HTTP/1.1" 200 OK
INFO:     **************:39896 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:39896 - "GET /sitemap.xml HTTP/1.1" 404 Not Found
INFO:     **************:39896 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     **************:55678 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     **************:18977 - "GET /robots.txt HTTP/1.1" 404 Not Found
INFO:     **************:45268 - "GET /sitemap.xml HTTP/1.1" 404 Not Found
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '1160', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 'NvZJt+cfbl5xWdl8IXZG9rDDKA79b06rskcrxuOp6jQ=', 'timestamp': '1749370617727', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-5jjwt', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Mac",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msgKUb9/OgLTiTaDO7s3Wsskg==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749376017714,
  "createAt": 1749370617559,
  "content": {
    "richText": [
      {
        "text": "@数据中台助手 这个图片上有什么"
      },
      {
        "pictureDownloadCode": "0K65X+BLSH2DFBRLpBaJzkISJKU8V/tmCwJpNQ0tb/IV5QrqWD2O8Qq1pMea/ywDqyQwTNmrlPzrgajFtUpQv4ZOfSTgrowCOf3MTRmLjp7vQ+yCa245tJBHtikLaYHZuNi5c/eb2afRKmQZpFgmL1MDssnh/H500ljiyDNTuMgSwhVVrmg6tu3ze7O+QQcD",
        "downloadCode": "mIofN681YE3f/+m+NntqpVgAHRBUYYbl/zahnJhazzfTZ+Gr1uZ71vZvJayUPulcgqFx7t6v5oGEjJ7sXftYZJtF7IPsdRHtBJicVnOYdWfwJbk8t4D2gu8m9suIf7ZHQg23EcbXeamYLNs/TE4FKhUg3fW091gqlOJdymyC1uTkiX5Go9d+IjzOAhuWS18k",
        "type": "picture"
      }
    ]
  },
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=c682d040b70496d23e1fae045061ac81",
  "robotCode": "normal",
  "msgtype": "richText"
}
ERROR:__main__:解析消息体失败: 1 validation error for DingTalkMessage
content
  Input should be a valid string [type=string_type, input_value={'richText': [{'text': '@...k', 'type': 'picture'}]}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
INFO:     **************:53978 - "POST /moye/dingtalk/webhook HTTP/1.1" 400 Bad Request
INFO:     Started server process [604525]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8900 (Press CTRL+C to quit)
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '1210', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': '3iBcnTny60RZGetPnow0KJoLxq6New+8Xfy5UIrQgN0=', 'timestamp': '1749372656467', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-rmjs6', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Mac",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msg7bHDuLmKq3aSu2HrpKXHYQ==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749378056400,
  "createAt": 1749372656163,
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=c682d040b70496d23e1fae045061ac81",
  "originalMsgId": "msgKUb9/OgLTiTaDO7s3Wsskg==",
  "text": {
    "isReplyMsg": true,
    "repliedMsg": {
      "createdAt": 1749370617559,
      "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
      "msgType": "richText",
      "msgId": "msgKUb9/OgLTiTaDO7s3Wsskg==",
      "content": {
        "richText": [
          {
            "msgType": "text",
            "content": "@数据中台助手 这个图片上有什么"
          },
          {
            "msgType": "picture",
            "downloadCode": "mIofN681YE3f/+m+NntqpVgAHRBUYYbl/zahnJhazzfTZ+Gr1uZ71vZvJayUPulcgqFx7t6v5oGEjJ7sXftYZJtF7IPsdRHtBJicVnOYdWfwJbk8t4D2gu8m9suIf7ZHG7DaLADaE1U8Xf/8iPH5Ch66ON0fv12349XdZxEkosqExil3A6ozaEiS6jUC4qOS"
          }
        ]
      }
    },
    "content": " 引用消息"
  },
  "robotCode": "normal",
  "msgtype": "text"
}
INFO:__main__:处理消息 - 发送者: 林伟, 内容: 引用消息
/TRS/moye-dingding-agent/main.py:122: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
  "atUsers": [user.dict() for user in message.atUsers] if message.atUsers else []
INFO:__main__:消息详情: {
  "msgId": "msg7bHDuLmKq3aSu2HrpKXHYQ==",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "senderNick": "林伟",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "conversationType": "群聊",
  "content": "引用消息",
  "timestamp": "1749372656467",
  "isAdmin": false,
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
      "staffId": null
    }
  ]
}
INFO:     **************:14985 - "POST /moye/dingtalk/webhook HTTP/1.1" 200 OK
Traceback (most recent call last):
  File "/TRS/moye-dingding-agent/main.py", line 10, in <module>
    from models import DingTalkMessage, DingTalkHeaders
  File "/TRS/moye-dingding-agent/models.py", line 11, in <module>
    class RepliedMsg(BaseModel):
  File "/TRS/moye-dingding-agent/models.py", line 17, in RepliedMsg
    content: Optional[Union[str, RichTextContent, Dict[str, Any]]] = None  # 原消息内容
NameError: name 'RichTextContent' is not defined
Traceback (most recent call last):
  File "/TRS/moye-dingding-agent/main.py", line 10, in <module>
    from models import DingTalkMessage, DingTalkHeaders
  File "/TRS/moye-dingding-agent/models.py", line 11, in <module>
    class TextContent(BaseModel):
  File "/TRS/moye-dingding-agent/models.py", line 15, in TextContent
    repliedMsg: Optional[RepliedMsg] = None         # 被回复的消息信息
NameError: name 'RepliedMsg' is not defined
INFO:     Started server process [800995]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8900 (Press CTRL+C to quit)
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '1163', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 'H4zo3Bh/5JHHJ6kc0rEjJsRzT/MXwS/WdrxmhIu8GPw=', 'timestamp': '1749431817994', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-rmjs6', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Mac",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msg81JQ+XyIJWKISk3JeQegNg==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749437217981,
  "createAt": 1749431817684,
  "content": {
    "richText": [
      {
        "text": "@数据中台助手 解读一下下面的问题"
      },
      {
        "pictureDownloadCode": "0K65X+BLSH2DFBRLpBaJzkISJKU8V/tmCwJpNQ0tb/IV5QrqWD2O8Qq1pMea/ywDSDo6P9qejOUM2OGIU1RQO1C6B9YFDDfUuoPoZNbDjrXKHc2lA17dg1UUMMEQLCSR54UC71FdbJvL+lmgcMesJT7K7AO2600+kS8pJHMPITXtGDwVzvtu0kSFmWFs4t4H",
        "downloadCode": "mIofN681YE3f/+m+Nntqpd57Ze73zOohOW7elsYGcqdyPO1ux4cUiGPoSbhwBDLG800illW/RM3OiXc9DzsvpDE905oJ5yl/E813knFgWkZ9o/vuDo+yYzaSDseyM7LEWecmJh/QEL6ZlLXJxmHs1lgeTXHxWdBPbTdxAxmEgk/YdiQl2P5lPlShXBw9xnsI",
        "type": "picture"
      }
    ]
  },
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=e29daf45c90de961317d18da73e5bcc8",
  "robotCode": "normal",
  "msgtype": "richText"
}
INFO:__main__:处理消息 - 发送者: 林伟, 内容: 
/TRS/moye-dingding-agent/main.py:130: PydanticDeprecatedSince20: The `dict` method is deprecated; use `model_dump` instead. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.5/migration/
  "atUsers": [user.dict() for user in message.atUsers] if message.atUsers else [],
INFO:__main__:消息详情: {
  "msgId": "msg81JQ+XyIJWKISk3JeQegNg==",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "senderNick": "林伟",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "conversationType": "群聊",
  "content": "",
  "timestamp": "1749431817994",
  "isAdmin": false,
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
      "staffId": null
    }
  ],
  "isReply": false,
  "repliedContent": null,
  "originalMsgId": null
}
INFO:     **************:19472 - "POST /moye/dingtalk/webhook HTTP/1.1" 200 OK
INFO:__main__:收到钉钉消息请求头: {'host': 'moyeai.trscd.com.cn', 'user-agent': 'okhttp/3.5.0', 'content-length': '1228', 'accept-encoding': 'gzip', 'content-type': 'application/json; charset=utf-8', 'sign': 't3ZN98qoPCzC6m9rz71+rwgmWqRpGLoIJqiT4g3e3Qw=', 'timestamp': '1749431891231', 'token': 'bbb64fd6-fd7d-4f20-8474-b55adfdae90e', 'x-forwarded-for': '**********', 'x-forwarded-host': 'moyeai.trscd.com.cn', 'x-forwarded-port': '443', 'x-forwarded-proto': 'https', 'x-forwarded-server': 'traefik-6c466b54f4-4f9kr', 'x-real-ip': '**********'}
INFO:__main__:收到钉钉消息体: {
  "senderPlatform": "Mac",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b"
    }
  ],
  "chatbotUserId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
  "msgId": "msgB5bYvPm8HQwg99wCnkdTTw==",
  "senderNick": "林伟",
  "isAdmin": false,
  "sessionWebhookExpiredTime": 1749437291194,
  "createAt": 1749431890947,
  "conversationType": "2",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "conversationTitle": "数据中台助手-TEST",
  "isInAtList": true,
  "sessionWebhook": "https://oapi.dingtalk.com/robot/sendBySession?session=e29daf45c90de961317d18da73e5bcc8",
  "originalMsgId": "msg81JQ+XyIJWKISk3JeQegNg==",
  "text": {
    "isReplyMsg": true,
    "repliedMsg": {
      "createdAt": 1749431817684,
      "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
      "msgType": "richText",
      "msgId": "msg81JQ+XyIJWKISk3JeQegNg==",
      "content": {
        "richText": [
          {
            "msgType": "text",
            "content": "@数据中台助手 解读一下下面的问题"
          },
          {
            "msgType": "picture",
            "downloadCode": "mIofN681YE3f/+m+Nntqpd57Ze73zOohOW7elsYGcqdyPO1ux4cUiGPoSbhwBDLG800illW/RM3OiXc9DzsvpDE905oJ5yl/E813knFgWkZ9o/vuDo+yYzaSDseyM7LEEN6LC37lGDKGA2BsJYo8QC9HMLP6gi6KOl/afjxAG9YX/3jU6A0g4MbZi01EVjqZ"
          }
        ]
      }
    },
    "content": " 这个有什么反馈吗？"
  },
  "robotCode": "normal",
  "msgtype": "text"
}
INFO:__main__:处理消息 - 发送者: 林伟, 内容: 这个有什么反馈吗？
INFO:__main__:这是一条回复消息，原消息内容: 
INFO:__main__:消息详情: {
  "msgId": "msgB5bYvPm8HQwg99wCnkdTTw==",
  "senderId": "$:LWCP_v1:$6lQ9ZHMcJtsx54ZWu8pnOA==",
  "senderNick": "林伟",
  "conversationId": "cid/ZmoyDr+pssYQcJJvR2/WA==",
  "conversationType": "群聊",
  "content": "这个有什么反馈吗？",
  "timestamp": "1749431891231",
  "isAdmin": false,
  "atUsers": [
    {
      "dingtalkId": "$:LWCP_v1:$NJ/PnPMV4/b3K81Dh+vx2pnnMSIAgG7b",
      "staffId": null
    }
  ],
  "isReply": true,
  "repliedContent": "",
  "originalMsgId": "msg81JQ+XyIJWKISk3JeQegNg=="
}
INFO:     **************:65495 - "POST /moye/dingtalk/webhook HTTP/1.1" 200 OK
