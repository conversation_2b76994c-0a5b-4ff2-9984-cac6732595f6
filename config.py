import os
from typing import Optional

class Config:
    """应用配置类"""
    
    # 应用配置
    APP_NAME = "数据中台智能助手"
    APP_SECRET = "qA1wxbgtee5OOO3fDV6WUJeGGpDx9FCTebLOhdovzV5kx1_yxi2vpGJscmsG1AzF"
    
    # 钉钉推送配置
    DINGTALK_WEBHOOK_URL: Optional[str] = os.getenv("DINGTALK_WEBHOOK_URL")
    DINGTALK_WEBHOOK_SECRET: Optional[str] = os.getenv("DINGTALK_WEBHOOK_SECRET")
    
    # 数据库配置
    DATABASE_URL = "sqlite:///./dingtalk_messages.db"
    
    @classmethod
    def is_push_configured(cls) -> bool:
        """检查是否配置了推送功能"""
        return bool(cls.DINGTALK_WEBHOOK_URL and cls.DINGTALK_WEBHOOK_SECRET)

# 默认配置（如果环境变量未设置）
if not Config.DINGTALK_WEBHOOK_URL:
    Config.DINGTALK_WEBHOOK_URL = "https://oapi.dingtalk.com/robot/send?access_token=a098ee17dfc7e49140e68c37faab97b2a26362d7b04fb2fe14a44b929901f8b6"

if not Config.DINGTALK_WEBHOOK_SECRET:
    Config.DINGTALK_WEBHOOK_SECRET = "SEC1fcd711b522c788457a81d7f3d27268d8752a34e838c67997ace9556f8f18d1a"