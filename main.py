from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import json
import hmac
import hashlib
import base64
import logging
from typing import Dict, Any
import httpx
import asyncio
import time
from datetime import datetime

from models import DingTalkMessage, DingTalkHeaders, RichTextContent
from database import DingTalkMessageRecord, init_db, get_db
from sqlalchemy.orm import Session
from config import Config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="钉钉机器人服务",
    description="接收和处理钉钉机器人消息的API服务",
    version="1.0.0"
)

# 配置静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

app_name = Config.APP_NAME
app_secret = Config.APP_SECRET

# 初始化数据库
init_db()

# 验证签名
def check_signature(timestamp: str, sign: str) -> bool:
    app_secret_enc = app_secret.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, app_secret)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(app_secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    gen_sign = base64.b64encode(hmac_code).decode('utf-8')
    return gen_sign == sign

# 生成推送签名
def generate_push_signature(timestamp: int) -> str:
    secret_enc = Config.DINGTALK_WEBHOOK_SECRET.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, Config.DINGTALK_WEBHOOK_SECRET)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    return base64.b64encode(hmac_code).decode('utf-8')

# 保存消息到数据库
def save_message_to_db(message: DingTalkMessage, timestamp: str, db: Session):
    try:
        # 判断是否为回复消息
        is_reply = False
        replied_content = ""
        if message.text and message.text.isReplyMsg and message.text.repliedMsg:
            is_reply = True
            replied_content = get_replied_message_content(message.text.repliedMsg)
        
        # 创建数据库记录
        db_message = DingTalkMessageRecord(
            msg_id=message.msgId,
            sender_id=message.senderId,
            sender_nick=message.senderNick,
            conversation_id=message.conversationId,
            conversation_type=message.conversationType,
            conversation_title=message.conversationTitle or "",
            content=get_message_content(message),
            msg_type=message.msgtype,
            create_at=datetime.fromtimestamp(int(message.createAt)/1000) if isinstance(message.createAt, (str, int)) and str(message.createAt).isdigit() else None,
            timestamp=timestamp,
            is_admin=message.isAdmin or False,
            is_reply=is_reply,
            replied_content=replied_content,
            original_msg_id=message.originalMsgId or "",
            at_users=json.dumps([user.dict() for user in message.atUsers]) if message.atUsers else "[]"
        )
        
        db.add(db_message)
        db.commit()
        db.refresh(db_message)
        logger.info(f"消息已保存到数据库: {message.msgId}")
        return db_message
    except Exception as e:
        db.rollback()
        logger.error(f"保存消息到数据库失败: {str(e)}")
        return None

# 向钉钉群推送消息
async def push_message_to_dingtalk(content: str, msg_type: str = "text"):
    # 检查是否配置了推送功能
    if not Config.is_push_configured():
        logger.info("未配置钉钉推送功能，跳过推送")
        return False
    
    try:
        timestamp = int(time.time() * 1000)  # 使用正确的Unix时间戳（毫秒）
        sign = generate_push_signature(timestamp)
        
        # 正确构建包含timestamp和sign参数的webhook URL
        webhook_url = f"{Config.DINGTALK_WEBHOOK_URL}&timestamp={timestamp}&sign={sign}"
        
        # 根据消息类型设置不同的payload格式
        if msg_type == "markdown":
            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "title": "新消息提醒",
                    "text": content
                }
            }
        else:
            payload = {
                "msgtype": "text",
                "text": {
                    "content": content
                }
            }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(webhook_url, json=payload, timeout=10.0)
            result = response.json()
            if result.get("errcode") == 0:
                logger.info("消息推送成功")
                return True
            else:
                logger.error(f"消息推送失败: {result}")
                return False
    except Exception as e:
        logger.error(f"推送消息时发生错误: {str(e)}")
        return False

@app.get("/")
async def root():
    """健康检查接口"""
    return {"message": "钉钉机器人服务运行正常", "status": "healthy"}


@app.post("/moye/dingtalk/webhook")
async def receive_dingtalk_message(request: Request, db: Session = Depends(get_db)):
    """
    接收钉钉机器人消息的Webhook接口
    
    Args:
        request: FastAPI请求对象，包含headers和body
        db: 数据库会话对象
        
    Returns:
        JSON响应
    """
    try:
        # 获取请求头
        headers = dict(request.headers)
        logger.info(f"收到钉钉消息请求头: {headers}")
        
        # 验证必要的请求头
        if "timestamp" not in headers:
            raise HTTPException(status_code=400, detail="缺少timestamp请求头")
        if "sign" not in headers:
            raise HTTPException(status_code=400, detail="缺少sign请求头")
        
        # 验证签名
        # if not check_signature(headers["timestamp"], headers["sign"]):
        #     raise HTTPException(status_code=400, detail="签名验证失败")
        
        # 解析请求头
        dingtalk_headers = DingTalkHeaders(
            timestamp=headers["timestamp"],
            sign=headers["sign"]
        )
        
        # 获取请求体
        body = await request.json()
        logger.info(f"收到钉钉消息体: {json.dumps(body, ensure_ascii=False, indent=2)}")
        
        # 验证并解析消息体
        try:
            message = DingTalkMessage(**body)
        except Exception as e:
            logger.error(f"解析消息体失败: {str(e)}")
            raise HTTPException(status_code=400, detail=f"消息体格式错误: {str(e)}")
        
        # 保存消息到数据库
        save_message_to_db(message, headers["timestamp"], db)
        
        # 处理消息
        response = await process_message(message, dingtalk_headers, db)
        
        # 异步推送消息到指定钉钉群
        content = get_message_content(message)
        print(f"收到新消息: {content}")

        if message.conversationType == "2":
            send_content = f"收到来自【{message.senderNick}】在群聊【{message.conversationTitle}】的新消息:\n<br> {content}"
        else:
            send_content = f"收到来自【{message.senderNick}】的新消息:\n<br> {content}"

        if message.text and message.text.isReplyMsg and message.text.repliedMsg:
            replied_content = get_replied_message_content(message.text.repliedMsg)
            send_content += f"\n> {replied_content}"

        # 使用markdown格式发送消息
        asyncio.create_task(push_message_to_dingtalk(send_content, "markdown"))
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"处理钉钉消息时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


async def process_message(message: DingTalkMessage, headers: DingTalkHeaders, db: Session) -> Dict[str, Any]:
    """
    处理钉钉消息的业务逻辑
    
    Args:
        message: 解析后的钉钉消息对象
        headers: 解析后的请求头对象
        db: 数据库会话对象
        
    Returns:
        处理结果
    """
    logger.info(f"处理消息 - 发送者: {message.senderNick}, 内容: {get_message_content(message)}")
    
    # 获取消息内容
    content = get_message_content(message)
    
    # 检查是否为回复消息
    is_reply = False
    replied_content = ""
    if message.text and message.text.isReplyMsg and message.text.repliedMsg:
        is_reply = True
        replied_content = get_replied_message_content(message.text.repliedMsg)
        logger.info(f"这是一条回复消息，原消息内容: {replied_content}")
    
    # 记录消息信息
    message_info = {
        "msgId": message.msgId,
        "senderId": message.senderId,
        "senderNick": message.senderNick,
        "conversationId": message.conversationId,
        "conversationType": "群聊" if message.conversationType == "2" else "单聊",
        "content": content,
        "timestamp": headers.timestamp,
        "isAdmin": message.isAdmin,
        "atUsers": [user.dict() for user in message.atUsers] if message.atUsers else [],
        "isReply": is_reply,
        "repliedContent": replied_content if is_reply else None,
        "originalMsgId": message.originalMsgId if message.originalMsgId else None
    }
    
    logger.info(f"消息详情: {json.dumps(message_info, ensure_ascii=False, indent=2)}")
    # 示例：简单的回复逻辑
    reply_content = await generate_reply(content, message)
    
    # 返回钉钉机器人格式的回复
    return {
        "msgtype": "text",
        "text": {
            "content": reply_content
        }
    }

def get_message_content(message: DingTalkMessage) -> str:
    """
    获取消息内容
    
    Args:
        message: 钉钉消息对象
        
    Returns:
        消息文本内容
    """
    if message.text and message.text.content:
        return message.text.content.strip()
    elif message.content:
        # 处理富文本消息
        if isinstance(message.content, dict) and "richText" in message.content:
            text_parts = []
            for item in message.content["richText"]:
                if isinstance(item, dict):
                    # 处理新格式：msgType + content
                    if item.get("msgType") == "text" and "content" in item:
                        text_parts.append(item["content"])
                    elif item.get("msgType") == "picture":
                        text_parts.append("[图片]")
                    # 兼容旧格式：type + text
                    elif "text" in item:
                        text_parts.append(item["text"])
                    elif item.get("type") == "picture":
                        text_parts.append("[图片]")
            return " ".join(text_parts).strip()
        # 处理字符串内容
        elif isinstance(message.content, str):
            return message.content.strip()
    
    return ""


def get_replied_message_content(replied_msg) -> str:
    """
    获取被回复消息的内容
    
    Args:
        replied_msg: 被回复的消息对象
        
    Returns:
        被回复消息的文本内容
    """
    if not replied_msg or not replied_msg.content:
        return ""
    
    # 处理富文本消息
    if isinstance(replied_msg.content, RichTextContent):
        text_parts = []
        for item in replied_msg.content.richText:
            # 处理新格式：msgType + content
            if item.msgType == "text":
                text_parts.append(item.content)
            elif item.msgType == "picture":
                text_parts.append("[图片]")
        return " ".join(text_parts).strip()
    elif isinstance(replied_msg.content, dict) and "text" in replied_msg.content:
        return replied_msg.content["text"].strip()
    # 处理字符串内容
    elif isinstance(replied_msg.content, str):
        return replied_msg.content.strip()
    
    return ""


async def generate_reply(content: str, message: DingTalkMessage, history_messages: list = None) -> str:
    """
    生成回复内容（示例实现）
    
    Args:
        content: 接收到的消息内容
        message: 钉钉消息对象
        history_messages: 历史消息列表
        
    Returns:
        回复内容
    """
    return f"@{message.senderNick}，你的信息已收到，我们会尽快与您联系！"
    # # 初始化历史消息列表
    # if history_messages is None:
    #     history_messages = []
    
    # # 这里可以集成AI服务或其他业务逻辑
    
    # # 检查是否为回复消息
    # is_reply_message = message.text and message.text.isReplyMsg and message.text.repliedMsg
    # replied_content = ""
    # if is_reply_message:
    #     replied_content = get_replied_message_content(message.text.repliedMsg)
    
    # # 检查是否包含图片（当前消息或被回复消息）
    # has_image = False
    # has_replied_image = False
    
    # # 检查当前消息是否有图片
    # if isinstance(message.content, dict) and "richText" in message.content:
    #     for item in message.content["richText"]:
    #         if isinstance(item, dict) and (item.get("type") == "picture" or item.get("msgType") == "picture"):
    #             has_image = True
    #             break
    
    # # 检查被回复消息是否有图片
    # if is_reply_message and message.text.repliedMsg.content:
    #     if isinstance(message.text.repliedMsg.content, dict) and "richText" in message.text.repliedMsg.content:
    #         for item in message.text.repliedMsg.content["richText"]:
    #             if isinstance(item, dict) and (item.get("type") == "picture" or item.get("msgType") == "picture"):
    #                 has_replied_image = True
    #                 break
    
    # # 构建回复上下文
    # context = {
    #     "is_reply": is_reply_message,
    #     "replied_content": replied_content,
    #     "has_image": has_image,
    #     "has_replied_image": has_replied_image,
    #     "history_messages": history_messages,
    #     "sender_nick": message.senderNick,
    #     "conversation_type": "群聊" if message.conversationType == "2" else "单聊"
    # }
    
    # # 简单的示例回复逻辑
    # if is_reply_message:
    #     if has_replied_image:
    #         return f"我看到您回复了一条包含图片的消息。\n原消息内容：{replied_content}\n您的回复：{content}\n关于图片内容，我暂时无法处理，但已记录您的消息。"
    #     else:
    #         return f"收到您的回复消息！\n原消息：{replied_content}\n您的回复：{content}\n正在为您处理这个回复..."
    # elif "你好" in content or "hello" in content.lower():
    #     return f"你好 {message.senderNick}！我是{app_name}，很高兴为您服务！\n这是我们的对话历史：\n{json.dumps(history_messages, ensure_ascii=False, indent=2)}"
    # elif "帮助" in content or "help" in content.lower():
    #     return "我可以帮助您处理各种问题，请告诉我您需要什么帮助。\n以下是一些常见问题的解决方案：..."
    # elif has_image:
    #     return f"收到您发送的图片消息：{content}\n很抱歉，我暂时无法处理图片内容，但我已经记录了您的消息。"
    # else:
    #     return f"收到您的消息：{content}\n正在为您处理，请稍等...\n上下文信息：\n{json.dumps(context, ensure_ascii=False, indent=2)}"


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={"success": False, "message": exc.detail, "status_code": exc.status_code}
    )


@app.get("/messages", response_class=HTMLResponse)
async def get_messages_page(request: Request):
    """返回消息查询页面的HTML"""
    return templates.TemplateResponse("messages.html", {"request": request})


@app.get("/api/messages")
async def get_messages(page: int = 1, size: int = 10, db: Session = Depends(get_db)):
    """
    分页获取消息记录接口
    
    Args:
        page: 页码，默认为1
        size: 每页数量，默认为10
        db: 数据库会话对象
        
    Returns:
        分页消息记录
    """
    try:
        # 计算总记录数
        total = db.query(DingTalkMessageRecord).count()
        
        # 计算总页数
        pages = (total + size - 1) // size
        
        # 确保页码有效
        if page < 1:
            page = 1
        if page > pages and pages > 0:
            page = pages
            
        # 查询当前页的数据
        messages = db.query(DingTalkMessageRecord).order_by(
            DingTalkMessageRecord.create_at.desc()
        ).offset((page - 1) * size).limit(size).all()
        
        # 转换为字典格式
        messages_data = []
        for message in messages:
            messages_data.append({
                "id": message.id,
                "msg_id": message.msg_id,
                "sender_id": message.sender_id,
                "sender_nick": message.sender_nick,
                "conversation_id": message.conversation_id,
                "conversation_type": message.conversation_type,
                "conversation_title": message.conversation_title,
                "content": message.content,
                "msg_type": message.msg_type,
                "create_at": message.create_at.isoformat() if message.create_at else None,
                "timestamp": message.timestamp,
                "is_admin": message.is_admin,
                "is_reply": message.is_reply,
                "replied_content": message.replied_content,
                "original_msg_id": message.original_msg_id,
                "at_users": message.at_users,
                "created_at": message.created_at.isoformat() if message.created_at else None
            })
        
        return {
            "success": True,
            "messages": messages_data,
            "total": total,
            "page": page,
            "pages": pages
        }
    except Exception as e:
        logger.error(f"查询消息记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail="查询消息记录失败")


@app.get("/api/messages/search")
async def search_messages(keyword: str, page: int = 1, size: int = 10, db: Session = Depends(get_db)):
    """
    搜索消息记录接口
    
    Args:
        keyword: 搜索关键词
        page: 页码，默认为1
        size: 每页数量，默认为10
        db: 数据库会话对象
        
    Returns:
        符合条件的分页消息记录
    """
    try:
        # 查询符合条件的总记录数
        total = db.query(DingTalkMessageRecord).filter(
            DingTalkMessageRecord.content.contains(keyword)
        ).count()
        
        # 计算总页数
        pages = (total + size - 1) // size
        
        # 确保页码有效
        if page < 1:
            page = 1
        if page > pages and pages > 0:
            page = pages
            
        # 查询当前页的数据
        messages = db.query(DingTalkMessageRecord).filter(
            DingTalkMessageRecord.content.contains(keyword)
        ).order_by(
            DingTalkMessageRecord.create_at.desc()
        ).offset((page - 1) * size).limit(size).all()
        
        # 转换为字典格式
        messages_data = []
        for message in messages:
            messages_data.append({
                "id": message.id,
                "msg_id": message.msg_id,
                "sender_id": message.sender_id,
                "sender_nick": message.sender_nick,
                "conversation_id": message.conversation_id,
                "conversation_type": message.conversation_type,
                "conversation_title": message.conversation_title,
                "content": message.content,
                "msg_type": message.msg_type,
                "create_at": message.create_at.isoformat() if message.create_at else None,
                "timestamp": message.timestamp,
                "is_admin": message.is_admin,
                "is_reply": message.is_reply,
                "replied_content": message.replied_content,
                "original_msg_id": message.original_msg_id,
                "at_users": message.at_users,
                "created_at": message.created_at.isoformat() if message.created_at else None
            })
        
        return {
            "success": True,
            "messages": messages_data,
            "total": total,
            "page": page,
            "pages": pages
        }
    except Exception as e:
        logger.error(f"搜索消息记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail="搜索消息记录失败")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8900) 