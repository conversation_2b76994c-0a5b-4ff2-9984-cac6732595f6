from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import json

Base = declarative_base()

class DingTalkMessageRecord(Base):
    """钉钉消息记录模型"""
    __tablename__ = 'dingtalk_messages'
    
    id = Column(Integer, primary_key=True, index=True)
    msg_id = Column(String, unique=True, index=True)  # 消息ID
    sender_id = Column(String)                        # 发送者ID
    sender_nick = Column(String)                      # 发送者昵称
    conversation_id = Column(String)                  # 会话ID
    conversation_type = Column(String)                # 会话类型(1:单聊 2:群聊)
    conversation_title = Column(String)               # 会话标题
    content = Column(Text)                            # 消息内容
    msg_type = Column(String)                         # 消息类型
    create_at = Column(DateTime)                      # 消息创建时间
    timestamp = Column(String)                        # 时间戳
    is_admin = Column(Boolean)                        # 是否为管理员
    is_reply = Column(Boolean)                        # 是否为回复消息
    replied_content = Column(Text)                    # 被回复消息内容
    original_msg_id = Column(String)                  # 原始消息ID
    at_users = Column(Text)                           # 被@的用户信息(JSON格式)
    created_at = Column(DateTime, default=datetime.utcnow)  # 记录创建时间
    
    def __repr__(self):
        return f"<DingTalkMessageRecord(msg_id='{self.msg_id}', sender='{self.sender_nick}')>"

# 数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./dingtalk_messages.db"

engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_db():
    """初始化数据库"""
    Base.metadata.create_all(bind=engine)

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()