let currentPage = 1;
const pageSize = 10;
let currentSearch = '';

// 页面加载完成后获取消息
window.onload = function() {
    loadMessages();
};

// 加载消息
function loadMessages(page = 1) {
    currentPage = page;
    fetch(`/api/messages?page=${page}&size=${pageSize}`)
        .then(response => response.json())
        .then(data => {
            displayMessages(data.messages);
            displayPagination(data.total, data.page, data.pages);
            document.getElementById('totalCount').textContent = data.total;
        })
        .catch(error => {
            console.error('获取消息失败:', error);
            alert('获取消息失败，请查看控制台了解详情');
        });
}

// 搜索消息
function searchMessages() {
    const keyword = document.getElementById('searchInput').value.trim();
    currentSearch = keyword;
    fetch(`/api/messages/search?keyword=${encodeURIComponent(keyword)}&page=1&size=${pageSize}`)
        .then(response => response.json())
        .then(data => {
            displayMessages(data.messages);
            displayPagination(data.total, data.page, data.pages);
            document.getElementById('totalCount').textContent = data.total;
        })
        .catch(error => {
            console.error('搜索消息失败:', error);
            alert('搜索消息失败，请查看控制台了解详情');
        });
}

// 显示消息
function displayMessages(messages) {
    const tbody = document.getElementById('messagesBody');
    tbody.innerHTML = '';
    
    if (messages.length === 0) {
        const row = tbody.insertRow();
        const cell = row.insertCell();
        cell.colSpan = 6;
        cell.textContent = '暂无消息记录';
        cell.style.textAlign = 'center';
        cell.style.padding = '20px';
        return;
    }
    
    messages.forEach(message => {
        const row = tbody.insertRow();
        
        // 发送者
        const senderCell = row.insertCell();
        senderCell.textContent = message.sender_nick;
        
        // 会话信息 (会话标题 + 会话类型)
        const conversationCell = row.insertCell();
        const conversationType = message.conversation_type === '2' ? '群聊' : '单聊';
        const conversationTitle = message.conversation_title || '无标题';
        conversationCell.innerHTML = `
            <div>${conversationTitle}</div>
            <div style="font-size: 0.8em; color: #666;">${conversationType}</div>
        `;
        
        // 消息内容
        const contentCell = row.insertCell();
        contentCell.className = 'content-cell';
        let contentHtml = `<div>${message.content || ''}</div>`;
        
        // 如果是回复消息，显示被回复的内容
        if (message.is_reply && message.replied_content) {
            contentHtml += `<div class="reply-content">回复: ${message.replied_content}</div>`;
        }
        
        contentCell.innerHTML = contentHtml;
        
        // 消息类型
        const typeCell = row.insertCell();
        typeCell.textContent = message.msg_type;
        
        // 创建时间
        const timeCell = row.insertCell();
        timeCell.textContent = formatDate(message.create_at);
        
        // 是否回复
        const replyCell = row.insertCell();
        replyCell.textContent = message.is_reply ? '是' : '否';
    });
}

// 显示分页控件
function displayPagination(total, currentPage, totalPages) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    if (totalPages <= 1) {
        return;
    }
    
    // 上一页按钮
    const prevButton = document.createElement('button');
    prevButton.textContent = '上一页';
    prevButton.disabled = currentPage === 1;
    prevButton.onclick = () => {
        if (currentSearch) {
            searchMessages(currentPage - 1);
        } else {
            loadMessages(currentPage - 1);
        }
    };
    pagination.appendChild(prevButton);
    
    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, startPage + 4);
    
    for (let i = startPage; i <= endPage; i++) {
        const pageButton = document.createElement('button');
        pageButton.textContent = i;
        pageButton.className = i === currentPage ? 'active' : '';
        pageButton.onclick = () => {
            if (currentSearch) {
                searchMessages(i);
            } else {
                loadMessages(i);
            }
        };
        pagination.appendChild(pageButton);
    }
    
    // 下一页按钮
    const nextButton = document.createElement('button');
    nextButton.textContent = '下一页';
    nextButton.disabled = currentPage === totalPages;
    nextButton.onclick = () => {
        if (currentSearch) {
            searchMessages(currentPage + 1);
        } else {
            loadMessages(currentPage + 1);
        }
    };
    pagination.appendChild(nextButton);
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 绑定回车键搜索
document.getElementById('searchInput').addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        searchMessages();
    }
});