body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
    color: #333;
    text-align: center;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background-color: #4CAF50;
    color: white;
}

tr:hover {
    background-color: #f5f5f5;
}

.content-cell {
    max-width: 300px;
    word-wrap: break-word;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination button {
    padding: 8px 16px;
    margin: 0 4px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    cursor: pointer;
}

.pagination button.active {
    background-color: #4CAF50;
    color: white;
}

.pagination button:hover:not(.active) {
    background-color: #ddd;
}

.search-box {
    margin-bottom: 20px;
}

.search-box input {
    width: 300px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.search-box button {
    padding: 10px 20px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
}

.message-count {
    text-align: right;
    margin-bottom: 10px;
    color: #666;
}

.reply-content {
    color: #666;
    font-style: italic;
    border-left: 3px solid #4CAF50;
    padding-left: 10px;
    margin-top: 5px;
}